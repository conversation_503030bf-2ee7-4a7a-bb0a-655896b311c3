# -*- encoding:utf-8 -*-
import numpy as np
import pandas as pd

from abupy import abu, ABuSymbolPd, ABuMarketDrawing, ABuKLUtil
from abupy import AbuFactorBuyBreak, AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop
from abupy import ABuUmpManager, ABuEnv
from abupy import AbuUmpMainDeg, AbuUmpEdgeDeg

def prepare_ump_training_data():
    """准备一个虚拟的裁判训练数据，实际使用中应来自真实回测"""
    # 仅为演示，创建虚拟的ump模型文件
    # 在实际应用中，你需要先运行一个回测，生成orders_pd，然后调用ump_main_clf_dump和ump_edge_clf_dump进行训练
    from abupy import AbuUmpMainBase, AbuUmpEdgeBase, AbuMetricsBase
    
    # 假设这是从一次回测中得到的orders_pd
    # 注意：这里的数据是随机生成的，不具备任何真实含义，仅用于能跑通流程
    num_orders = 200
    orders_data = {
        'symbol': ['usTSLA'] * num_orders,
        'buy_date': pd.to_datetime(np.random.choice(pd.date_range('2022-01-01', '2023-01-01'), num_orders)),
        'buy_price': np.random.uniform(100, 200, num_orders),
        'buy_cnt': [100] * num_orders,
        'sell_date': pd.to_datetime(np.random.choice(pd.date_range('2023-01-02', '2023-06-01'), num_orders)),
        'sell_price': np.random.uniform(80, 220, num_orders),
        'sell_type': ['win'] * num_orders,
        'key': np.arange(num_orders),
        'result': np.random.choice([-1, 1], num_orders), # -1 loss, 1 win
        'buy_deg_ang42': np.random.randn(num_orders) * 10,
        'buy_deg_ang252': np.random.randn(num_orders) * 10,
        'buy_deg_ang60': np.random.randn(num_orders) * 10,
        'buy_deg_ang21': np.random.randn(num_orders) * 10,
    }
    orders_pd = pd.DataFrame(orders_data)
    orders_pd['profit'] = (orders_pd['sell_price'] - orders_pd['buy_price']) * orders_pd['buy_cnt']
    
    # 需要通过AbuMetricsBase计算'profit_cg'等度量
    try:
        metrics = AbuMetricsBase(orders_pd, None, None, None)
        metrics.fit_metrics_order()
        orders_pd_trained = metrics.orders_pd
    except Exception as e:
        # 在某些环境下，fit_metrics_order可能会因为数据问题失败，这里做一个兼容
        print(f"Warning: fit_metrics_order failed with {e}, using original orders_pd.")
        orders_pd_trained = orders_pd

    print("正在生成演示用的主裁判模型文件...")
    # 训练并保存主裁判
    AbuUmpMainBase.ump_main_clf_dump(orders_pd_trained, market_name='my_main_ump', brust_min=False, show_info=False)
    print("主裁判模型文件生成完毕。")

    print("正在生成演示用的边裁判模型文件...")
    # 训练并保存边裁判
    AbuUmpEdgeBase.ump_edge_clf_dump(orders_pd_trained, market_name='my_edge_ump', show_info=False)
    print("边裁判模型文件生成完毕。\n")


# 1. 先准备好用于演示的裁判模型文件
prepare_ump_training_data()

# 2. 设置回测参数
read_cash = 1000000
# 使用沙盒数据
stock_pool = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']

# 买入因子
buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},
               {'xd': 42, 'class': AbuFactorBuyBreak}]

# 卖出因子
sell_factors = [{'stop_loss_n': 1.0, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop},
                {'class': AbuFactorPreAtrNStop},
                {'class': AbuFactorCloseAtrNStop}]

# 3. *****核心步骤：配置并启用裁判系统*****
print("配置裁判系统中...")
# 3.1. 开启用户自定义裁判开关
ABuUmpManager.g_enable_user_ump = True
# 3.2. 开启特征记录功能（裁判系统依赖此功能）
ABuEnv.g_enable_ml_feature = True
# 3.3. (最佳实践) 每次运行前清空之前的裁判设置
ABuUmpManager.clear_user_ump()

# 3.4. 实例化需要使用的裁判
# 实例化主裁判，predict=True代表用于预测，market_name指定加载哪个模型
ump_main = AbuUmpMainDeg(predict=True, market_name='my_main_ump')
# 实例化边裁判
ump_edge = AbuUmpEdgeDeg(predict=True, market_name='my_edge_ump')

# 3.5. 将裁判实例添加到管理器中
ABuUmpManager.append_user_ump(ump_main)
ABuUmpManager.append_user_ump(ump_edge)

print(f"已配置 {len(ABuUmpManager._g_extend_ump_list)} 个裁判: {ump_main}, {ump_edge}")
print("裁判系统配置完毕，开始执行回测...\n")

# 4. 执行回测
abu_result_tuple, kl_pd_manager = abu.run_loop_back(read_cash, buy_factors, sell_factors, stock_picks=None, choice_symbols=stock_pool, n_folds=2)

# 5. 分析回测结果
if abu_result_tuple is not None and abu_result_tuple[0] is not None and not abu_result_tuple[0].empty:
    from abupy import ABuMetricsBase
    metrics = ABuMetricsBase(*abu_result_tuple)
    metrics.fit_metrics()
    metrics.plot_returns_cmp(only_show_returns=True)
else:
    print("回测未产生任何交易，无法进行度量分析。这可能是由于裁判系统拦截了所有信号。")

# 6. (最佳实践) 回测结束后，可以关闭开关，避免影响其他不使用裁判的回测
ABuUmpManager.g_enable_user_ump = False
ABuEnv.g_enable_ml_feature = False
print("\n回测完成，已关闭裁判系统开关。")