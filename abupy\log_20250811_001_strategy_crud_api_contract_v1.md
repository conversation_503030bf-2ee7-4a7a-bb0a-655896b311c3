# 策略CRUD API数据契约 V1.0

## 文档概述

本文档详细描述了abu_modern后端"策略CRUD"的API数据契约，为前端"策略工场"提供精确的数据格式规范。

**生成时间**: 2025-08-11  
**版本**: V1.0  
**适用范围**: abu_modern前后端策略管理模块  

---

## 1. 创建策略 (Create Strategy)

### 1.1 基本信息

- **端点**: `POST /api/v1/strategy/`
- **请求体模型**: `StrategyCreate`
- **响应状态码**: 201 (创建成功)
- **响应模型**: `SuccessResponse[Strategy]`

### 1.2 StrategyCreate 模型详解

| 字段名 | 数据类型 | 是否必需 | 默认值 | 描述 |
|--------|----------|----------|--------|---------|
| `name` | `str` | **必需** | - | 策略名称，最小长度1 |
| `description` | `str` | 可选 | `null` | 策略描述 |
| `is_public` | `bool` | 可选 | `false` | 是否公开策略 |
| `buy_factors` | `List[BuyFactor]` | **必需** | - | 买入因子列表，至少包含1个 |
| `sell_factors` | `List[SellFactor]` | 可选 | `[]` | 卖出因子列表 |
| `position_strategy` | `PositionStrategy` | 可选 | `null` | 仓位管理策略 |
| `umpire_market_name` | `str` | 可选 | `null` | 裁判市场名称 |
| `umpire_rules` | `List[Dict[str, Any]]` | 可选 | `null` | 裁判规则列表 |
| `parameters` | `Dict[str, Any]` | 可选 | `{}` | 策略参数字典 |
| `tags` | `List[str]` | 可选 | `null` | 策略标签列表 |

### 1.3 嵌套结构详解

#### 1.3.1 BuyFactor 模型

| 字段名 | 数据类型 | 是否必需 | 描述 |
|--------|----------|----------|---------|
| `id` | `str` | 可选 | 因子唯一ID |
| `name` | `str` | **必需** | 因子名称 |
| `description` | `str` | 可选 | 因子描述 |
| `factor_type` | `str` | 自动设置 | 固定值"buy" |
| `class_name` | `str` | **必需** | 因子对应的类名 |
| `parameters` | `Dict[str, Any]` | 可选 | 因子参数字典 |

#### 1.3.2 SellFactor 模型

| 字段名 | 数据类型 | 是否必需 | 描述 |
|--------|----------|----------|---------|
| `id` | `str` | 可选 | 因子唯一ID |
| `name` | `str` | **必需** | 因子名称 |
| `description` | `str` | 可选 | 因子描述 |
| `factor_type` | `str` | 自动设置 | 固定值"sell" |
| `class_name` | `str` | **必需** | 因子对应的类名 |
| `parameters` | `Dict[str, Any]` | 可选 | 因子参数字典 |

#### 1.3.3 PositionStrategy 模型

| 字段名 | 数据类型 | 是否必需 | 描述 |
|--------|----------|----------|---------|
| `class_name` | `str` | **必需** | 仓位管理策略的类名 |
| `parameters` | `Dict[str, Any]` | 可选 | 仓位管理策略的参数 |

### 1.4 完整JSON示例

```json
{
  "name": "双均线突破策略",
  "description": "基于双均线突破的量化交易策略",
  "is_public": true,
  "buy_factors": [
    {
      "name": "双均线突破买入因子",
      "description": "当短期均线突破长期均线时买入",
      "class_name": "FactorBuyBreak",
      "parameters": {
        "xd": 20,
        "short_period": 5,
        "long_period": 20
      }
    }
  ],
  "sell_factors": [
    {
      "name": "N日后卖出因子",
      "description": "持有N日后自动卖出",
      "class_name": "FactorSellNDay",
      "parameters": {
        "sell_n": 5
      }
    }
  ],
  "position_strategy": {
    "class_name": "AbuPositionBase",
    "parameters": {
      "position_mode": "equal_weight"
    }
  },
  "parameters": {
    "initial_capital": 1000000,
    "commission_rate": 0.001
  },
  "tags": ["双均线", "突破", "短线"]
}
```

---

## 2. 更新策略 (Update Strategy)

### 2.1 基本信息

- **端点**: `PUT /api/v1/strategy/{strategy_id}`
- **请求体模型**: `StrategyUpdate`
- **响应状态码**: 200 (更新成功)
- **响应模型**: `SuccessResponse[Strategy]`

### 2.2 StrategyUpdate 模型详解

| 字段名 | 数据类型 | 是否必需 | 默认值 | 描述 |
|--------|----------|----------|--------|---------|
| `name` | `str` | 可选 | `null` | 策略名称 |
| `description` | `str` | 可选 | `null` | 策略描述 |
| `is_public` | `bool` | 可选 | `null` | 是否公开策略 |
| `buy_factors` | `List[BuyFactor]` | 可选 | `null` | 买入因子列表 |
| `sell_factors` | `List[SellFactor]` | 可选 | `null` | 卖出因子列表 |
| `position_strategy` | `PositionStrategy` | 可选 | `null` | 仓位管理策略 |
| `umpire_market_name` | `str` | 可选 | `null` | 裁判市场名称 |
| `umpire_rules` | `List[Dict[str, Any]]` | 可选 | `null` | 裁判规则列表 |
| `parameters` | `Dict[str, Any]` | 可选 | `null` | 策略参数字典 |
| `tags` | `List[str]` | 可选 | `null` | 策略标签列表 |

**注意**: StrategyUpdate模型中所有字段都是可选的，支持部分更新。未提供的字段将保持原值不变。

### 2.3 嵌套结构详解

嵌套结构（BuyFactor、SellFactor、PositionStrategy）的定义与创建策略时相同，详见第1.3节。

### 2.4 完整JSON示例

```json
{
  "name": "优化后的双均线突破策略",
  "description": "经过参数优化的双均线突破策略，提高了收益率",
  "buy_factors": [
    {
      "name": "优化双均线突破买入因子",
      "description": "优化参数后的双均线突破买入条件",
      "class_name": "FactorBuyBreak",
      "parameters": {
        "xd": 15,
        "short_period": 3,
        "long_period": 15
      }
    }
  ],
  "parameters": {
    "initial_capital": 2000000,
    "commission_rate": 0.0008,
    "max_position_ratio": 0.8
  },
  "tags": ["双均线", "突破", "优化", "中线"]
}
```

---

## 3. 其他相关API端点

### 3.1 获取策略详情

- **端点**: `GET /api/v1/strategy/{strategy_id}`
- **响应模型**: `SuccessResponse[Strategy]`

### 3.2 获取策略列表

- **端点**: `GET /api/v1/strategy/`
- **查询参数**: `skip`, `limit`, `owner`, `is_public`
- **响应模型**: `PaginatedResponse[Strategy]`

### 3.3 删除策略

- **端点**: `DELETE /api/v1/strategy/{strategy_id}`
- **响应模型**: `SuccessResponse[Dict[str, str]]`

### 3.4 执行策略回测

- **端点**: `POST /api/v1/strategy/{strategy_id}/execute`
- **请求体模型**: `StrategyExecuteRequest`
- **响应模型**: `SuccessResponse[Dict[str, Any]]`

### 3.5 获取可用因子列表

- **端点**: `GET /api/v1/strategy/factors/`
- **查询参数**: `factor_type` (可选: "buy" 或 "sell")
- **响应模型**: `Dict[str, List[Dict[str, Any]]]`

---

## 4. 数据验证规则

### 4.1 自动验证规则

1. **因子类型自动设置**: 在`StrategyCreate`中，系统会自动为买入因子设置`factor_type="buy"`，为卖出因子设置`factor_type="sell"`

2. **因子名称自动生成**: 如果因子没有提供`name`字段，系统会根据`class_name`自动生成名称

3. **时间戳自动管理**: 创建时自动设置`create_time`和`update_time`，更新时自动更新`update_time`

### 4.2 业务验证规则

1. **策略名称**: 必须至少包含1个字符
2. **买入因子**: 创建策略时必须至少包含1个买入因子
3. **因子类名**: 必须是有效的因子类名
4. **参数格式**: 所有参数必须是有效的JSON格式

---

## 5. 错误响应格式

### 5.1 标准错误响应

```json
{
  "success": false,
  "message": "错误描述信息",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "具体错误字段",
    "reason": "错误原因"
  }
}
```

### 5.2 常见错误状态码

- **400 Bad Request**: 输入数据验证失败
- **404 Not Found**: 策略不存在
- **422 Unprocessable Entity**: 业务逻辑验证失败
- **500 Internal Server Error**: 服务器内部错误

---

## 6. 使用注意事项

### 6.1 前端开发建议

1. **必需字段验证**: 在发送请求前，确保所有必需字段都已填写
2. **参数类型检查**: 确保数字类型参数不要作为字符串发送
3. **错误处理**: 根据不同的错误状态码提供相应的用户提示
4. **部分更新**: 更新策略时只发送需要修改的字段

### 6.2 数据格式要求

1. **日期格式**: 所有日期时间字段使用ISO 8601格式
2. **字符编码**: 使用UTF-8编码
3. **数值精度**: 浮点数保留适当的小数位数
4. **字符串长度**: 注意各字段的长度限制

---

## 7. 版本更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| V1.0 | 2025-01-15 | 初始版本，包含完整的策略CRUD API数据契约 |

---

**文档结束**

> 本文档基于abu_modern后端代码分析生成，如有疑问请参考源码：
> - `backend/app/schemas/strategy.py`
> - `backend/app/api/endpoints/strategy.py`