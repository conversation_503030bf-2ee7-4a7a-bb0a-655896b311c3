
# This file was generated by 'versioneer.py' (0.16) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json
import sys

version_json = '''
{
 "dirty": false,
 "error": null,
 "full-revisionid": "5d38634e0d68e65699383d2a97edaf34ce91fada",
 "version": "0.2.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
